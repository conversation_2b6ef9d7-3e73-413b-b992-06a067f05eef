# EduTech Platform

A modern educational technology platform built with Next.js, featuring AI-powered chatbot capabilities, document management, and secure authentication.

## ✨ Features

- **🔐 Authentication System**: Integrated login and registration forms with Google OAuth support
- **📚 Interactive Learning**: Personalized courses with expert guidance
- **🎯 Progress Tracking**: Certified learning paths and achievements
- **👥 Community Support**: Connect with fellow learners
- **📱 Responsive Design**: Optimized for all devices with modern UI/UX

## 🚀 Tech Stack

### Frontend
- **Framework**: [Next.js 15](https://nextjs.org/) with App Router
- **UI Library**: [HeroUI](https://heroui.com/) - Modern React components
- **Styling**: [Tailwind CSS](https://tailwindcss.com/)
- **Server Actions**: [next-safe-action](https://next-safe-action.dev/) - Type-safe server actions
- **Language**: TypeScript
- **Deployment**: [Vercel](https://vercel.com/)

### Backend & Services
- **Authentication**: [Microsoft Entra External ID](https://learn.microsoft.com/en-us/entra/external-id/)
- **Database**: Azure Cosmos DB / Azure Table Storage (TBD)
- **Cache**: [Azure Cache for Redis](https://azure.microsoft.com/en-us/products/cache/)
- **AI Search**: [Azure AI Search](https://azure.microsoft.com/en-us/products/search/) with RAG (Retrieval-Augmented Generation)

## 🌟 Features

- **🔐 User Authentication**: Secure sign-up and login with Microsoft Entra External ID
- **🤖 AI Chatbot**: Intelligent conversational AI powered by RAG and Azure AI Search
- **📁 Document Management**: Upload, store, and organize educational materials
- **📚 Document Hub**: Browse and search through uploaded documents
- **🎨 Modern UI**: Clean, responsive interface built with HeroUI components
- **🌙 Dark Mode**: Full dark/light theme support
- **⚡ Performance**: Optimized with Next.js App Router and Azure caching
- **🔒 Type Safety**: End-to-end type safety with next-safe-action server actions

## 🛠 Getting Started

### Prerequisites

- Node.js 18.0 or later
- Bun (recommended) or npm
- Azure account with required services configured
- Microsoft Entra External ID tenant

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd edutech
   ```

2. **Install dependencies**
   ```bash
   bun install
   ```

3. **Environment Configuration**
   Create a `.env.local` file in the root directory:
   ```env
   # Microsoft Entra External ID
   NEXT_PUBLIC_MICROSOFT_CLIENT_ID=your_client_id
   MICROSOFT_CLIENT_SECRET=your_client_secret
   MICROSOFT_TENANT_ID=your_tenant_id
   
   # Azure Services
   AZURE_COSMOSDB_CONNECTION_STRING=your_cosmosdb_connection
   AZURE_REDIS_CONNECTION_STRING=your_redis_connection
   AZURE_AI_SEARCH_ENDPOINT=your_search_endpoint
   AZURE_AI_SEARCH_API_KEY=your_search_api_key
   
   # Next.js
   NEXTAUTH_URL=http://localhost:3000
   NEXTAUTH_SECRET=your_nextauth_secret
   ```

4. **Run the development server**
   ```bash
   bun dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
edutech/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── (auth)/            # Authentication pages
│   │   ├── chatbot/           # AI Chatbot interface
│   │   ├── documents/         # Document management
│   │   ├── hub/               # Document Hub
│   │   └── api/               # API routes
│   ├── components/            # Reusable UI components
│   ├── lib/                   # Utility functions and configurations
│   │   ├── actions/           # Server actions with next-safe-action
│   │   └── validations/       # Zod schemas for validation
│   ├── hooks/                 # Custom React hooks
│   └── types/                 # TypeScript type definitions
├── public/                    # Static assets
└── docs/                      # Documentation
```

## 🔧 Available Scripts

- `bun dev` - Start development server with Turbopack
- `bun build` - Build production application
- `bun start` - Start production server
- `bun lint` - Run ESLint
- `bun type-check` - Run TypeScript type checking

## 🌐 Deployment

### Vercel Deployment

1. **Connect your repository to Vercel**
   - Push your code to GitHub/GitLab/Bitbucket
   - Import project in Vercel dashboard

2. **Configure environment variables**
   - Add all environment variables from `.env.local` to Vercel

3. **Deploy**
   ```bash
   vercel --prod
   ```

### Azure Services Setup

1. **Microsoft Entra External ID**
   - Create External ID tenant
   - Configure application registration
   - Set up redirect URIs

2. **Azure Cosmos DB / Table Storage**
   - Create database instance
   - Configure connection strings
   - Set up collections/tables

3. **Azure Cache for Redis**
   - Create Redis instance
   - Configure connection string

4. **Azure AI Search**
   - Create search service
   - Configure indexers and data sources
   - Set up RAG pipeline

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📚 Documentation

- [Next.js Documentation](https://nextjs.org/docs)
- [HeroUI Documentation](https://heroui.com/docs)
- [next-safe-action Documentation](https://next-safe-action.dev/)
- [Microsoft Entra External ID Documentation](https://learn.microsoft.com/en-us/entra/external-id/)
- [Azure AI Search Documentation](https://learn.microsoft.com/en-us/azure/search/)

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🔮 Roadmap

- [ ] Enhanced document processing and indexing
- [ ] Advanced AI chat capabilities
- [ ] Multi-language support
- [ ] Mobile application
- [ ] Integration with more educational platforms
- [ ] Analytics and reporting dashboard

## 🐛 Issues & Support

If you encounter any issues or have questions, please:
1. Check the [documentation](docs/)
2. Search existing [issues](../../issues)
3. Create a new issue with detailed information

---

**Built with ❤️ for education**
