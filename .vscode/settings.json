{"css.validate": false, "less.validate": false, "scss.validate": false, "css.customData": [{"version": 1.1, "atDirectives": [{"name": "@apply", "description": "Use the @apply directive to inline any existing utility classes into your own custom CSS."}, {"name": "@theme", "description": "Use the @theme directive to define your project's custom design tokens."}, {"name": "@utility", "description": "Use the @utility directive to add custom utilities to your project."}, {"name": "@variant", "description": "Use the @variant directive to apply a Tailwind variant to styles in your CSS."}, {"name": "@custom-variant", "description": "Use the @custom-variant directive to add a custom variant in your project."}, {"name": "@source", "description": "Use the @source directive to explicitly specify source files."}, {"name": "@reference", "description": "Use the @reference directive to import your main stylesheet for reference."}, {"name": "@config", "description": "Use the @config directive to load a legacy JavaScript-based configuration file."}, {"name": "@plugin", "description": "Use the @plugin directive to load a legacy JavaScript-based plugin."}]}], "tailwindCSS.experimental.configFile": null, "tailwindCSS.includeLanguages": {"typescript": "javascript", "typescriptreact": "javascript"}, "tailwindCSS.classAttributes": ["class", "className", "ngClass"]}