/* 確保中文字體正確加載 - 必須在所有其他 @import 之前 */
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Noto+Sans+TC:wght@300;400;500;700&display=swap');

/* Tailwind CSS 和 daisyUI 配置 */
@import "tailwindcss";
@plugin "daisyui";

/* Content paths for Tailwind CSS v4 */
@source "./src/**/*.{js,ts,jsx,tsx,mdx}";
@source "./pages/**/*.{js,ts,jsx,tsx,mdx}";
@source "./components/**/*.{js,ts,jsx,tsx,mdx}";
@source "./app/**/*.{js,ts,jsx,tsx,mdx}";

/* Tailwind CSS v4 Theme Configuration */
@theme {
  --font-family-sans: 'Noto Sans TC', 'Roboto', <PERSON><PERSON>, sans-serif;
}

/* 自定義樣式 */
body {
  font-family: 'Noto Sans TC', 'Roboto', Arial, sans-serif;
}

/* 自定義工具類 */
.demo-section {
  @apply my-8;
}

.demo-title {
  @apply mb-4;
}

/* 響應式調整 */
@media (max-width: 600px) {
  .demo-section {
    @apply my-4;
  }
}

/* Enhanced Auth Page Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUpDelay {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced form slide-in animation with scale */
@keyframes formSlideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes enhancedFormSlideIn {
  0% {
    opacity: 0;
    transform: translateX(30px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* Floating label animation */
@keyframes floatingLabel {
  0% {
    transform: translateY(0) scale(1);
    color: hsl(var(--bc) / 0.6);
  }
  100% {
    transform: translateY(-20px) scale(0.85);
    color: hsl(var(--p));
  }
}

/* Button press animation */
@keyframes buttonPress {
  0% { transform: scale(1); }
  50% { transform: scale(0.98); }
  100% { transform: scale(1); }
}

/* Form transition animations */
@keyframes formFadeOut {
  0% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateX(-30px) scale(0.95);
  }
}

@keyframes formFadeIn {
  0% {
    opacity: 0;
    transform: translateX(30px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* Input field glow animation */
@keyframes inputGlow {
  0% {
    box-shadow: 0 0 0 0 hsl(var(--p) / 0.4);
  }
  70% {
    box-shadow: 0 0 0 4px hsl(var(--p) / 0.1);
  }
  100% {
    box-shadow: 0 0 0 0 hsl(var(--p) / 0);
  }
}

/* Success pulse animation */
@keyframes successPulse {
  0% {
    box-shadow: 0 0 0 0 hsl(var(--su) / 0.4);
  }
  70% {
    box-shadow: 0 0 0 4px hsl(var(--su) / 0.1);
  }
  100% {
    box-shadow: 0 0 0 0 hsl(var(--su) / 0);
  }
}

/* Animation Classes */
.animate-fade-in {
  animation: fadeIn 0.8s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.8s ease-out 0.2s both;
}

.animate-slide-up-delay {
  animation: slideUpDelay 0.8s ease-out 0.4s both;
}

.animate-form-slide-in {
  animation: formSlideIn 0.6s ease-out;
}

.animate-enhanced-form-slide-in {
  animation: enhancedFormSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-form-fade-out {
  animation: formFadeOut 0.3s ease-in-out forwards;
}

.animate-form-fade-in {
  animation: formFadeIn 0.3s ease-in-out;
}

.animate-button-press {
  animation: buttonPress 0.15s ease-in-out;
}

.animate-input-glow {
  animation: inputGlow 0.6s ease-out;
}

.animate-success-pulse {
  animation: successPulse 0.6s ease-out;
}

/* Auth Form Container */
.auth-form-container {
  position: relative;
  overflow: hidden;
}

.auth-form-container > * {
  transition: all 0.3s ease-in-out;
}



/* Custom Input Focus Effects */
.input:focus {
  outline: none;
  box-shadow: 0 0 0 3px hsl(var(--p) / 0.2);
  border-color: hsl(var(--p));
  transform: none;
  animation: inputGlow 0.6s ease-out;
}

.input:focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px hsl(var(--p) / 0.2);
  border-color: hsl(var(--p));
}

/* Enhanced Button Hover Effects */
.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px hsl(var(--p) / 0.3);
}

.btn-outline:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px hsl(var(--bc) / 0.15);
  border-color: hsl(var(--bc) / 0.3);
  background-color: hsl(var(--bc) / 0.05);
}

/* Enhanced Form Input Styling */
.input {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-width: 2px;
}

.input:hover {
  border-color: hsl(var(--bc) / 0.4);
  box-shadow: 0 2px 8px hsl(var(--bc) / 0.1);
}

/* Success state for inputs */
.input-success {
  border-color: hsl(var(--su));
  background-color: hsl(var(--su) / 0.05);
}

.input-success:focus {
  border-color: hsl(var(--su));
  box-shadow: 0 0 0 3px hsl(var(--su) / 0.2);
  animation: successPulse 0.6s ease-out;
}

/* Enhanced error state for inputs */
.input-error {
  border-color: hsl(var(--er));
  background-color: hsl(var(--er) / 0.05);
  animation: shake 0.3s ease-in-out;
}

.input-error:focus {
  border-color: hsl(var(--er));
  box-shadow: 0 0 0 3px hsl(var(--er) / 0.2);
}

/* Shake animation for error states */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-4px); }
  75% { transform: translateX(4px); }
}

/* Floating label styles */
.floating-label {
  position: relative;
}

.floating-label label {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: hsl(var(--b1));
  padding: 0 4px;
  color: hsl(var(--bc) / 0.6);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
  z-index: 1;
}

.floating-label input:focus + label,
.floating-label input:not(:placeholder-shown) + label {
  top: 0;
  transform: translateY(-50%) scale(0.85);
  color: hsl(var(--p));
  font-weight: 500;
}

/* Enhanced Google Button Styling */
.google-btn {
  border: 1px solid hsl(var(--bc) / 0.2);
  background: transparent;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

/* Smooth shadow and background transition */
.google-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.04);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.google-btn:hover {
  border-color: hsl(var(--bc) / 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.google-btn:hover::before {
  opacity: 1;
}

.google-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
  transition: all 0.1s ease;
}

/* Welcome Title Animation */
@keyframes titleFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-title {
  animation: titleFadeIn 0.6s ease-out;
}

/* Rounded Button Styles */
.btn-rounded {
  border-radius: 0.75rem;
}

/* Mobile Responsive Adjustments for Auth Page */
@media (max-width: 1024px) {
  .auth-form-container {
    padding: 1rem;
  }
}

/* Dark Theme Specific Styles */
.dark-gradient-bg {
  background: linear-gradient(135deg, #1e293b 0%, #581c87 50%, #334155 100%);
}



/* Enhanced Button Press Effects and Micro-interactions */
.btn:active {
  animation: buttonPress 0.15s ease-in-out;
}

/* Enhanced button hover effects for all interactive elements */
.btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  transform-origin: center;
}

.btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1), height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
  z-index: 0;
}

.btn:hover::before {
  width: 300px;
  height: 300px;
}

/* Enhanced button text and icon positioning */
.btn > * {
  position: relative;
  z-index: 1;
}

/* Enhanced primary button effects */
.btn-primary {
  background: linear-gradient(135deg, hsl(var(--p)), hsl(var(--p) / 0.8));
  border: none;
  box-shadow: 0 2px 8px hsl(var(--p) / 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 6px 20px hsl(var(--p) / 0.4);
  background: linear-gradient(135deg, hsl(var(--p) / 0.9), hsl(var(--p) / 0.7));
}

.btn-primary:active {
  transform: translateY(-1px) scale(1.01);
  box-shadow: 0 3px 12px hsl(var(--p) / 0.5);
}

/* Enhanced outline button effects */
.btn-outline {
  border: 2px solid hsl(var(--bc) / 0.3);
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
}

.btn-outline:hover {
  transform: translateY(-2px) scale(1.02);
  border-color: hsl(var(--p) / 0.6);
  background: hsl(var(--p) / 0.1);
  box-shadow: 0 6px 20px hsl(var(--bc) / 0.2);
}

.btn-outline:active {
  transform: translateY(-1px) scale(1.01);
  box-shadow: 0 3px 12px hsl(var(--bc) / 0.3);
}

/* Enhanced loading state animations */
@keyframes spinEnhanced {
  0% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
  100% { transform: rotate(360deg) scale(1); }
}

@keyframes pulseGlow {
  0%, 100% { 
    box-shadow: 0 0 0 0 hsl(var(--p) / 0.4);
    opacity: 1;
  }
  50% { 
    box-shadow: 0 0 0 8px hsl(var(--p) / 0);
    opacity: 0.8;
  }
}

@keyframes loadingDots {
  0%, 20% { opacity: 0; transform: scale(0.8); }
  50% { opacity: 1; transform: scale(1.2); }
  100% { opacity: 0; transform: scale(0.8); }
}

/* Enhanced loading button styles */
.btn.loading {
  position: relative;
  color: transparent;
  animation: pulseGlow 2s infinite;
}

.btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spinEnhanced 1s linear infinite;
}

/* Loading dots animation for form submission */
.btn.loading-dots::after {
  content: '•••';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.5rem;
  letter-spacing: 2px;
  animation: loadingDots 1.5s infinite;
}

/* Clickable element hover effects */
.clickable-element {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.clickable-element:hover {
  transform: translateY(-1px);
  filter: brightness(1.1);
}

.clickable-element:active {
  transform: translateY(0) scale(0.98);
}

/* Link hover effects */
.link {
  position: relative;
  transition: all 0.3s ease;
}

.link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: currentColor;
  transition: width 0.3s ease;
}

.link:hover::after {
  width: 100%;
}

.link:hover {
  transform: translateY(-1px);
}

/* Checkbox and form control enhancements */
.checkbox {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.checkbox:hover {
  transform: scale(1.1);
  box-shadow: 0 0 0 4px hsl(var(--p) / 0.1);
}

.checkbox:checked {
  animation: checkboxPop 0.3s ease-out;
}

@keyframes checkboxPop {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* Form submission button state transitions */
.btn-submit {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-submit:not(:disabled):hover {
  transform: translateY(-3px) scale(1.03);
  box-shadow: 0 8px 25px hsl(var(--p) / 0.4);
}

.btn-submit:not(:disabled):active {
  transform: translateY(-1px) scale(1.01);
  transition-duration: 0.1s;
}

/* Success state animation for buttons */
@keyframes successBounce {
  0% { transform: scale(1); }
  25% { transform: scale(1.1); }
  50% { transform: scale(0.95); }
  75% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.btn-success-state {
  background: linear-gradient(135deg, hsl(var(--su)), hsl(var(--su) / 0.8));
  animation: successBounce 0.6s ease-out;
}

/* Error state animation for buttons */
@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.btn-error-state {
  background: linear-gradient(135deg, hsl(var(--er)), hsl(var(--er) / 0.8));
  animation: errorShake 0.5s ease-out;
}

/* Enhanced password visibility toggle button */
.password-toggle {
  transition: all 0.2s ease;
  border-radius: 50%;
  padding: 4px;
}

.password-toggle:hover {
  background: hsl(var(--bc) / 0.1);
  transform: scale(1.1);
}

.password-toggle:active {
  transform: scale(0.95);
  background: hsl(var(--bc) / 0.2);
}

/* Ripple effect for buttons */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

.btn-ripple {
  position: relative;
  overflow: hidden;
}

.btn-ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn-ripple:active::before {
  width: 300px;
  height: 300px;
  transition: 0s;
}

/* Micro-interaction for form field icons */
.field-icon {
  transition: all 0.3s ease;
}

.input:focus ~ .field-icon,
.input:focus + .field-icon {
  color: hsl(var(--p));
  transform: scale(1.1);
}

/* Simple divider styling without animation */
.divider {
  position: relative;
  overflow: visible;
}

.divider::before {
  animation: none;
  transform: none;
}

/* Password Strength Indicator Animations */
@keyframes strengthBarGrow {
  0% {
    width: 0%;
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes strengthPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Password Strength Bar Styles */
.password-strength-container {
  margin-top: 0.5rem;
  padding: 0.75rem;
  background: hsl(var(--b2));
  border-radius: 0.5rem;
  border: 1px solid hsl(var(--bc) / 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.password-strength-bar {
  height: 6px;
  background: hsl(var(--bc) / 0.1);
  border-radius: 3px;
  overflow: hidden;
  position: relative;
  margin-bottom: 0.5rem;
}

.password-strength-fill {
  height: 100%;
  border-radius: 3px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: strengthBarGrow 0.6s ease-out;
  position: relative;
}

.password-strength-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Strength Level Colors */
.strength-weak {
  background: linear-gradient(90deg, hsl(var(--er)), hsl(var(--er) / 0.8));
}

.strength-fair {
  background: linear-gradient(90deg, hsl(var(--wa)), hsl(var(--wa) / 0.8));
}

.strength-good {
  background: linear-gradient(90deg, hsl(var(--in)), hsl(var(--in) / 0.8));
}

.strength-strong {
  background: linear-gradient(90deg, hsl(var(--su)), hsl(var(--su) / 0.8));
}

/* Strength Text Styles */
.strength-text {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.875rem;
  font-weight: 500;
}

.strength-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.strength-icon {
  width: 1rem;
  height: 1rem;
  transition: all 0.3s ease;
}

/* Strength Requirements List */
.strength-requirements {
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid hsl(var(--bc) / 0.1);
}

.strength-requirement {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: hsl(var(--bc) / 0.6);
  transition: all 0.3s ease;
  margin-bottom: 0.25rem;
}

.strength-requirement.met {
  color: hsl(var(--su));
}

.strength-requirement.unmet {
  color: hsl(var(--bc) / 0.4);
}

.requirement-icon {
  width: 0.875rem;
  height: 0.875rem;
  flex-shrink: 0;
}

/* Animated check mark */
@keyframes checkMark {
  0% {
    transform: scale(0) rotate(45deg);
  }
  50% {
    transform: scale(1.2) rotate(45deg);
  }
  100% {
    transform: scale(1) rotate(45deg);
  }
}

.requirement-check {
  animation: checkMark 0.3s ease-out;
}

/* Accessibility: Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .animate-fade-in,
  .animate-slide-up,
  .animate-slide-up-delay,
  .animate-form-slide-in,
  .animate-enhanced-form-slide-in,
  .animate-title {
    animation: none;
    opacity: 1;
    transform: none;
  }
}

/* Login button disabled state styling */
.btn-disabled-grey {
  background-color: #e5e7eb;
  color: #9ca3af;
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;
}

.btn-disabled-grey:hover {
  background-color: #e5e7eb;
  color: #9ca3af;
  transform: none;
  box-shadow: none;
}

/* Enhanced Micro-interactions for All Interactive Elements */

/* Button press feedback with enhanced timing */
@keyframes enhancedButtonPress {
  0% { 
    transform: scale(1) translateY(0); 
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  }
  50% { 
    transform: scale(0.96) translateY(2px); 
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  }
  100% { 
    transform: scale(1) translateY(0); 
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  }
}

/* Enhanced button hover with magnetic effect */
@keyframes magneticHover {
  0% { transform: translateY(0) scale(1); }
  100% { transform: translateY(-3px) scale(1.02); }
}

/* Button loading state with enhanced visual feedback */
@keyframes loadingPulse {
  0%, 100% { 
    opacity: 1;
    transform: scale(1);
    box-shadow: 0 0 0 0 hsl(var(--p) / 0.4);
  }
  50% { 
    opacity: 0.8;
    transform: scale(1.02);
    box-shadow: 0 0 0 10px hsl(var(--p) / 0);
  }
}

/* Enhanced ripple effect with better timing */
@keyframes enhancedRipple {
  0% {
    transform: scale(0);
    opacity: 0.8;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* Smooth button state transitions */
.btn {
  will-change: transform, box-shadow, background;
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
}

.btn:active {
  animation: enhancedButtonPress 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced primary button interactions */
.btn-primary:not(:disabled) {
  background: linear-gradient(135deg, hsl(var(--p)), hsl(var(--p) / 0.85));
  box-shadow: 0 4px 12px hsl(var(--p) / 0.25);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-primary:not(:disabled):hover {
  animation: magneticHover 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  background: linear-gradient(135deg, hsl(var(--p) / 0.95), hsl(var(--p) / 0.8));
  box-shadow: 0 8px 25px hsl(var(--p) / 0.35);
}

.btn-primary:not(:disabled):focus {
  outline: none;
  box-shadow: 0 0 0 3px hsl(var(--p) / 0.3), 0 8px 25px hsl(var(--p) / 0.35);
}

/* Enhanced outline button interactions */
.btn-outline:not(:disabled):hover {
  animation: magneticHover 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  border-color: hsl(var(--p) / 0.7);
  background: hsl(var(--p) / 0.08);
  color: hsl(var(--p));
}

/* Enhanced loading state for buttons */
.btn.loading {
  animation: loadingPulse 2s infinite;
  pointer-events: none;
  position: relative;
}

.btn.loading::after {
  border-top-color: rgba(255, 255, 255, 0.8);
  animation: spinEnhanced 1.2s linear infinite;
}

/* Enhanced ripple effect implementation */
.btn-ripple:active::before {
  animation: enhancedRipple 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smooth hover effects for all clickable elements */
.clickable-element {
  will-change: transform, filter;
  backface-visibility: hidden;
}

.clickable-element:hover {
  animation: magneticHover 0.25s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  filter: brightness(1.05) saturate(1.1);
}

.clickable-element:active {
  animation: enhancedButtonPress 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced link interactions */
.link {
  will-change: transform;
}

.link:hover {
  animation: magneticHover 0.2s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.link::after {
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced form submission button states */
.btn-submit:not(:disabled) {
  background: linear-gradient(135deg, hsl(var(--p)), hsl(var(--p) / 0.85));
  box-shadow: 0 4px 15px hsl(var(--p) / 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-submit:not(:disabled):hover {
  transform: translateY(-4px) scale(1.03);
  box-shadow: 0 12px 30px hsl(var(--p) / 0.4);
  background: linear-gradient(135deg, hsl(var(--p) / 0.95), hsl(var(--p) / 0.8));
}

.btn-submit:not(:disabled):active {
  transform: translateY(-2px) scale(1.01);
  transition-duration: 0.1s;
  box-shadow: 0 6px 20px hsl(var(--p) / 0.5);
}

/* Enhanced checkbox interactions */
.checkbox:hover {
  transform: scale(1.15);
  box-shadow: 0 0 0 6px hsl(var(--p) / 0.08);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.checkbox:active {
  transform: scale(1.05);
  transition-duration: 0.1s;
}

/* Enhanced password toggle button */
.password-toggle {
  will-change: transform, background;
}

.password-toggle:hover {
  background: hsl(var(--bc) / 0.12);
  transform: scale(1.15) rotate(5deg);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.password-toggle:active {
  transform: scale(0.9) rotate(-5deg);
  background: hsl(var(--bc) / 0.2);
  transition-duration: 0.1s;
}

/* Enhanced form field icon interactions */
.field-icon {
  will-change: transform, color;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.input:focus ~ .field-icon,
.input:focus + .field-icon {
  color: hsl(var(--p));
  transform: scale(1.15) rotate(5deg);
}

/* Enhanced Google button with better feedback */
.google-btn:not(:disabled) {
  will-change: transform, box-shadow;
}

.google-btn:not(:disabled):focus {
  outline: none;
  box-shadow: 0 0 0 2px hsl(var(--bc) / 0.2), 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Simple divider without animation */
.divider {
  position: relative;
}

/* Enhanced form container interactions */
.auth-form-container {
  will-change: transform;
}

/* Enhanced input field interactions */
.input {
  will-change: border-color, box-shadow, transform;
}

.input:hover {
  transform: translateY(-1px);
  border-color: hsl(var(--bc) / 0.5);
  box-shadow: 0 4px 12px hsl(var(--bc) / 0.08);
}

.input:focus {
  transform: translateY(-2px);
  animation: inputGlow 0.6s ease-out;
}

/* Success and error state enhancements */
.input-success:focus {
  transform: translateY(-2px);
  animation: successPulse 0.6s ease-out;
}

.input-error {
  animation: shake 0.4s ease-in-out;
}

/* Enhanced button disabled state with subtle feedback */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  animation: none !important;
  transition: opacity 0.3s ease;
}

.btn:disabled:hover {
  transform: none !important;
  box-shadow: none !important;
  animation: none !important;
}

/* Performance optimizations for animations */
@media (prefers-reduced-motion: no-preference) {
  .btn,
  .clickable-element,
  .link,
  .input,
  .checkbox,
  .password-toggle,
  .field-icon {
    transform: translateZ(0);
    will-change: transform;
  }
}

/* MOBILE RESPONSIVE ENHANCEMENTS */

/* Enhanced Form Container with Better Backdrop Blur */
.auth-form-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 2px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 1rem;
  position: relative;
  overflow: hidden;
}

/* Backdrop filter fallback for unsupported browsers */
@supports not (backdrop-filter: blur(20px)) {
  .auth-form-container {
    background: rgba(255, 255, 255, 0.98);
    border: 2px solid rgba(0, 0, 0, 0.1);
  }
}

/* Mobile-First Touch Target Optimization */
.mobile-touch-target {
  min-height: 48px;
  min-width: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* Enhanced Mobile Input Styling */
.mobile-input {
  min-height: 48px;
  font-size: 16px; /* Prevents zoom on iOS */
  padding: 12px 16px;
  border-radius: 0.75rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced Mobile Button Styling */
.mobile-button {
  min-height: 48px;
  padding: 12px 20px;
  font-size: 16px;
  border-radius: 0.75rem;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced Google Button with Smoother Transitions */
.google-btn {
  --shadow-color: rgba(0, 0, 0, 0.08);
  --shadow-hover-color: rgba(0, 0, 0, 0.12);
  --border-color: rgba(0, 0, 0, 0.12);
  --border-hover-color: rgba(0, 0, 0, 0.2);
  --bg-hover-color: rgba(0, 0, 0, 0.02);
  
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid var(--border-color);
  box-shadow: 0 2px 6px var(--shadow-color);
  background-color: white;
  position: relative;
  overflow: hidden;
  border-radius: 0.75rem;
  min-height: 48px;
  will-change: transform, box-shadow, border-color;
}

.google-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-hover-color);
  opacity: 0;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: inherit;
}

.google-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px var(--shadow-hover-color);
  border-color: var(--border-hover-color);
  scale: 1.01;
}

.google-btn:hover::before {
  opacity: 1;
}

.google-btn:active {
  transform: translateY(-1px);
  transition-duration: 0.1s;
  scale: 0.99;
}

/* Theme-aware Google Button Styling */
@media (prefers-color-scheme: light) {
  .google-btn {
    --border-color: rgba(0, 0, 0, 0.15);
    --border-hover-color: rgba(0, 0, 0, 0.25);
    --bg-hover-color: rgba(0, 0, 0, 0.03);
    --shadow-color: rgba(0, 0, 0, 0.1);
    --shadow-hover-color: rgba(0, 0, 0, 0.15);
  }
}

@media (prefers-color-scheme: dark) {
  .google-btn {
    --border-color: rgba(255, 255, 255, 0.15);
    --border-hover-color: rgba(255, 255, 255, 0.25);
    --bg-hover-color: rgba(255, 255, 255, 0.05);
    --shadow-color: rgba(0, 0, 0, 0.2);
    --shadow-hover-color: rgba(0, 0, 0, 0.3);
    background-color: rgba(255, 255, 255, 0.05);
  }
}

/* Enhanced Password Toggle with Better Touch Target */
.password-toggle {
  min-width: 44px;
  min-height: 44px;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.password-toggle::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: hsl(var(--bc) / 0.1);
  opacity: 0;
  transition: opacity 0.25s ease;
  border-radius: inherit;
}

.password-toggle:hover::before {
  opacity: 1;
}

.password-toggle:hover {
  transform: scale(1.1);
}

.password-toggle:active {
  transform: scale(0.95);
  transition-duration: 0.1s;
}

/* Mobile-specific responsive adjustments */
@media (max-width: 640px) {
  .auth-form-container {
    margin: 0.75rem;
    padding: 1.5rem;
    border-radius: 1rem;
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
  }
  
  .mobile-form-spacing {
    gap: 1rem;
  }
  
  .mobile-text-size {
    font-size: 0.875rem;
  }
  
  /* Optimize touch targets on mobile */
  .btn {
    min-height: 48px;
    padding: 12px 16px;
  }
  
  .input {
    min-height: 48px;
    font-size: 16px;
  }
  
  /* Reduce excessive animations on mobile */
  .btn:hover {
    transform: translateY(-1px);
  }
  
  .input:hover {
    transform: none;
  }
  
  .input:focus {
    transform: none;
  }
}

/* Tablet breakpoint optimizations */
@media (min-width: 641px) and (max-width: 1024px) {
  .auth-form-container {
    padding: 2rem;
    margin: 1rem;
  }
  
  .mobile-button {
    min-height: 52px;
    padding: 14px 24px;
  }
  
  .mobile-input {
    min-height: 52px;
    padding: 14px 18px;
  }
}

/* Large screen optimizations */
@media (min-width: 1025px) {
  .auth-form-container {
    padding: 2rem;
    backdrop-filter: blur(24px);
    -webkit-backdrop-filter: blur(24px);
  }
  
  .mobile-button {
    min-height: 54px;
    padding: 16px 28px;
  }
  
  .mobile-input {
    min-height: 54px;
    padding: 16px 20px;
  }
}

/* GPU acceleration for mobile performance */
.gpu-accelerated {
  will-change: transform, opacity;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* Mobile-specific animation optimizations */
@media (max-width: 640px) {
  .animate-fade-in {
    animation-duration: 0.5s;
  }
  
  .animate-slide-up {
    animation-duration: 0.5s;
    animation-delay: 0.1s;
  }
  
  .animate-slide-up-delay {
    animation-duration: 0.5s;
    animation-delay: 0.2s;
  }
  
  .animate-form-slide-in {
    animation-duration: 0.4s;
  }
  
  .animate-title {
    animation-duration: 0.4s;
  }
}

/* Enhanced Link Touch Targets */
.link {
  position: relative;
  padding: 0.5rem;
  margin: -0.5rem;
  border-radius: 0.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: hsl(var(--bc) / 0.05);
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: inherit;
}

.link:hover::before {
  opacity: 1;
}

/* Enhanced Checkbox Touch Targets */
.checkbox {
  position: relative;
  cursor: pointer;
}

.checkbox::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: transparent;
  transition: background 0.3s ease;
  pointer-events: none;
  z-index: -1;
}

.checkbox:hover::before {
  background: hsl(var(--p) / 0.1);
}

/* Enhanced form validation feedback */
.form-error {
  font-size: 0.875rem;
  color: hsl(var(--er));
  margin-top: 0.5rem;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.form-error.show {
  opacity: 1;
  transform: translateY(0);
}

/* Enhanced success feedback */
.form-success {
  font-size: 0.875rem;
  color: hsl(var(--su));
  margin-top: 0.5rem;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.form-success.show {
  opacity: 1;
  transform: translateY(0);
}

/* Improved focus indicators for accessibility */
.btn:focus-visible {
  outline: 2px solid hsl(var(--p));
  outline-offset: 2px;
}

.input:focus-visible {
  outline: 2px solid hsl(var(--p));
  outline-offset: 2px;
}

.checkbox:focus-visible {
  outline: 2px solid hsl(var(--p));
  outline-offset: 2px;
}

/* Reduced motion enhancements */
@media (prefers-reduced-motion: reduce) {
  .auth-form-container,
  .google-btn,
  .password-toggle,
  .link,
  .checkbox,
  .btn,
  .input {
    transition: none !important;
    animation: none !important;
  }
  
  .google-btn:hover,
  .btn:hover,
  .input:hover {
    transform: none !important;
    scale: 1 !important;
  }
  
  .animate-fade-in,
  .animate-slide-up,
  .animate-slide-up-delay,
  .animate-form-slide-in,
  .animate-title {
    animation: none !important;
    opacity: 1 !important;
    transform: none !important;
  }
}