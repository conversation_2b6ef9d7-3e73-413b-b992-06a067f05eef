{"name": "edutech", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@azure/cosmos": "^4.4.1", "@azure/data-tables": "^13.3.1", "@azure/identity": "^4.10.2", "@azure/msal-browser": "^4.15.0", "@azure/msal-react": "^3.0.15", "@azure/search-documents": "^12.1.0", "@azure/storage-blob": "^12.27.0", "@edge-csrf/nextjs": "^2.5.3-cloudflare-rc1", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "daisyui": "^5.0.46", "dompurify": "^3.2.6", "dotenv": "^17.2.0", "framer-motion": "^12.23.5", "lucide-react": "^0.525.0", "multer": "^2.0.1", "next": "15.4.1", "next-auth": "^4.24.11", "next-safe-action": "^8.0.7", "next-secure-headers": "^2.2.0", "rate-limiter-flexible": "^7.1.1", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "redis": "^5.6.0", "theme-change": "^2.5.0", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.4.1", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "^5"}}